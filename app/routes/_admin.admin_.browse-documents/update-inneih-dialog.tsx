import type { UpdateRecordType } from './schema'
import type { GetDocumentsQuery } from '~/gql/graphql'
import { format } from 'date-fns'
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '~/components/ui/dialog'
import { Input } from '~/components/ui/input'
import { Label } from '~/components/ui/label'
import { useAppForm } from '~/hooks/form'
import useUpdateDocuments from './use-update-documents'

type InneihRecordFromQuery = NonNullable<NonNullable<GetDocumentsQuery['getDocuments']['data']>[0]['extra_record']> & { __typename: 'InneihRecord' }
type DocumentFromQuery = NonNullable<GetDocumentsQuery['getDocuments']['data']>[0]

interface Props {
  isOpen: boolean
  toggle: (open: boolean) => void
  record: InneihRecordFromQuery
  _document: DocumentFromQuery
}

export default function UpdateInneihDialog({ isOpen, toggle, record, _document }: Props) {
  const { updateInneihRecord } = useUpdateDocuments()

  const form = useAppForm({
    defaultValues: {
      inneih_record: {
        id: record.id,
        registration_no: record.inneih_registration_no || '',
        mipa_hming: record.mipa_hming || '',
        mipa_pa_hming: record.mipa_pa_hming || '',
        mipa_khua: record.mipa_khua || '',
        hmeichhe_hming: record.hmeichhe_hming || '',
        hmeichhe_pa_hming: record.hmeichhe_pa_hming || '',
        hmeichhe_khua: record.hmeichhe_khua || '',
        hmun: record.hmun || '',
        inneih_ni: record.inneih_ni ? format(new Date(record.inneih_ni), 'yyyy-MM-dd') : '',
        inneihtirtu: record.inneihtirtu || '',
      },
      document: {
        id: _document.id,
        title: _document.title || '',
        body: _document.body || '',
        tags: _document.tags || '',
        is_classified: _document.is_classified || false,
        category_id: _document.category_id || '',
        added_date: _document.added_date ? format(new Date(_document.added_date), 'yyyy-MM-dd') : '',
        files: undefined as File[] | undefined,
      },
    } as UpdateRecordType,
    onSubmit: async ({ value }) => {
      await updateInneihRecord.mutateAsync(value, {
        onSuccess: () => {
          toggle(false)
        },
      })
    },
  })

  return (
    <Dialog open={isOpen} onOpenChange={toggle}>
      <DialogContent className="w-full min-w-256">
        <DialogHeader>
          <DialogTitle>
            Update Inneih Record
          </DialogTitle>
          <DialogDescription>
            Enter update details
          </DialogDescription>
        </DialogHeader>
        <form
          onSubmit={(e) => {
            e.preventDefault()
            e.stopPropagation()
            form.handleSubmit()
          }}
          className="flex flex-col gap-4"
        >
          <div className="flex gap-x-8">
            <div className="w-full">
              <form.AppField
                name="inneih_record.mipa_hming"
                children={field => <field.InputField label="Mipa hming" />}
              />
            </div>
            <div className="w-full">
              <form.AppField
                name="inneih_record.mipa_pa_hming"
                children={field => <field.InputField label="Mipa pa hming" />}
              />
            </div>
          </div>
          <div className="flex gap-x-8">
            <div className="w-full">
              <form.AppField
                name="inneih_record.mipa_khua"
                children={field => <field.InputField label="Mipa khua" />}
              />
            </div>
            <div className="w-full">
              <form.AppField
                name="inneih_record.hmeichhe_hming"
                children={field => <field.InputField label="Hmeichhe hming" />}
              />
            </div>
          </div>
          <div className="flex gap-x-8">
            <div className="w-full">
              <form.AppField
                name="inneih_record.hmeichhe_pa_hming"
                children={field => <field.InputField label="Hmeichhe pa hming" />}
              />
            </div>
            <div className="w-full">
              <form.AppField
                name="inneih_record.hmeichhe_khua"
                children={field => <field.InputField label="Hmeichhe khua" />}
              />
            </div>
          </div>
          <div className="flex gap-x-8">
            <div className="w-full">
              <form.AppField
                name="inneih_record.inneih_ni"
                children={field => <field.InputField label="Inneih ni" type="date" />}
              />
            </div>
            <div className="w-full">
              <form.AppField
                name="inneih_record.hmun"
                children={field => <field.InputField label="Hmun" />}
              />
            </div>
          </div>
          <div className="flex gap-x-8">
            <div className="basis-1/2">
              <form.AppField
                name="inneih_record.inneihtirtu"
                children={field => <field.InputField label="Inneihtirtu" />}
              />
            </div>
            <div className="basis-1/2">
              <form.AppField
                name="inneih_record.registration_no"
                children={field => <field.InputField label="Registration No" />}
              />
            </div>
          </div>
          <h3 className="text-lg font-semibold">Document Information</h3>
          <div className="flex gap-x-4">
            <div className="w-full">
              <form.AppField
                name="document.title"
                children={field => <field.InputField label="Title" />}
              />
            </div>
            <div className="w-full">
              <form.AppField
                name="document.tags"
                children={field => <field.PillInput label="Tags" />}
              />
            </div>
          </div>
          <div className="w-full">
            <form.AppField
              name="document.body"
              children={field => <field.InputRichText label="Body" />}
            />
          </div>
          <div className="flex gap-x-4">
            <div className="w-full">
              <form.Field
                name="document.files"
                children={field => (
                  <Label className="flex flex-col items-start gap-y-2">
                    <div>Upload file</div>
                    <Input
                      type="file"
                      multiple={true}
                      accept="image/*,application/pdf"
                      onChange={(e) => {
                        if (e.target.files && e.target.files.length > 0) {
                          field.handleChange(Array.from(e.target.files))
                        }
                        else {
                          field.handleChange([])
                        }
                      }}
                      className="bg-white"
                    />

                  </Label>
                )}
              />
            </div>
            <div className="w-full">
              <form.AppField
                name="document.added_date"
                children={field => <field.InputField type="date" label="Added date" />}
              />
            </div>
          </div>
          <div className="flex gap-x-4">
            <div className="w-full">
              <form.AppField
                name="document.category_id"
                children={field => <field.InputField label="Category ID" />}
              />
            </div>
          </div>
          <div>
            <form.AppField
              name="document.is_classified"
              children={field => <field.CheckboxField label="Classified" />}
            />
          </div>
          <DialogFooter>
            <form.AppForm>
              <form.SubmitButton label="Update" />
            </form.AppForm>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  )
}
