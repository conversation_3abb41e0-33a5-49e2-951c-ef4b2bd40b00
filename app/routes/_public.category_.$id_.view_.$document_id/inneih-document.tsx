import type { FragmentType } from '~/gql'
import type { GetDocumentByIdQuery } from '~/gql/graphql'
import { FileText } from 'lucide-react'
import { useFragment } from '~/gql'
import { INNEIH_RECORD_FRAGMENT } from '../_public.category_.$id/graphql'

interface Props {
  record: FragmentType<typeof INNEIH_RECORD_FRAGMENT>
  files: NonNullable<GetDocumentByIdQuery['getDocumentById']>['files']
}

export default function InneihDocument({ record, files }: Props) {
  const inneihRecord = useFragment(INNEIH_RECORD_FRAGMENT, record)

  const generateLink = (_fileId: string) => {
    // TODO: Implement file download/view functionality
  }

  return (
    <div className="mt-2">
      {inneihRecord && (
        <div className="mt-2 w-full rounded-t-md bg-white shadow">
          <div className="border-b-2 border-gold px-6 py-4 text-xl">
            INFORMATION
          </div>
          <div className="grid grid-cols-12">
            <div className="col-span-3">
              <div className="grid grid-rows-12">
                <div className={`
                  row-span-1 border-r-2 border-b-2 border-gold px-6 py-4
                `}
                >
                  Mipa hming
                </div>
                <div className={`
                  row-span-1 border-r-2 border-b-2 border-gold px-6 py-4
                `}
                >
                  Mipa Pa hming
                </div>
                <div className={`
                  row-span-1 border-r-2 border-b-2 border-gold px-6 py-4
                `}
                >
                  Hmeichhe hming
                </div>
                <div className={`
                  row-span-1 border-r-2 border-b-2 border-gold px-6 py-4
                `}
                >
                  Hmeichhe Pa hming
                </div>
                <div className={`
                  row-span-1 border-r-2 border-b-2 border-gold px-6 py-4
                `}
                >
                  Inneih ni
                </div>
                <div className={`
                  row-span-1 border-r-2 border-b-2 border-gold px-6 py-4
                `}
                >
                  A hmun
                </div>
                <div className={`
                  row-span-1 border-r-2 border-b-2 border-gold px-6 py-4
                `}
                >
                  Inneihtirtu
                </div>
                <div className="row-span-5 h-full border-r-2 border-gold" />
              </div>
            </div>
            <div className="col-span-6">
              <div className="grid grid-rows-12">
                <div className={`
                  row-span-1 border-r-2 border-b-2 border-gold px-6 py-4
                `}
                >
                  {inneihRecord.mipa_hming || '\u00A0'}
                </div>
                <div className={`
                  row-span-1 border-r-2 border-b-2 border-gold px-6 py-4
                `}
                >
                  {inneihRecord.mipa_pa_hming || '\u00A0'}
                </div>
                <div className={`
                  row-span-1 border-r-2 border-b-2 border-gold px-6 py-4
                `}
                >
                  {inneihRecord.hmeichhe_hming || '\u00A0'}
                </div>
                <div className={`
                  row-span-1 border-r-2 border-b-2 border-gold px-6 py-4
                `}
                >
                  {inneihRecord.hmeichhe_pa_hming || '\u00A0'}
                </div>
                <div className={`
                  row-span-1 border-r-2 border-b-2 border-gold px-6 py-4
                `}
                >
                  {inneihRecord.inneih_ni || '\u00A0'}
                </div>
                <div className={`
                  row-span-1 border-r-2 border-b-2 border-gold px-6 py-4
                `}
                >
                  {inneihRecord.hmun || '\u00A0'}
                </div>
                <div className={`
                  row-span-1 border-r-2 border-b-2 border-gold px-6 py-4
                `}
                >
                  {inneihRecord.inneihtirtu || '\u00A0'}
                </div>
                <div className="row-span-5 h-full border-r-2 border-gold" />
              </div>
            </div>
            <div className="col-span-3">
              {files && files.length > 0 && (
                <div className="mt-2 px-4 py-4">
                  <div className="grid grid-cols-2 gap-x-2 gap-y-4">
                    {files.map(item => (
                      <div key={item.id} className="col-span-1">
                        <button
                          type="button"
                          className="focus:outline-none"
                          onClick={() => generateLink(item.id)}
                        >
                          <FileText className="text-footerBlack mx-4 text-9xl" />
                          <div className="px-4 text-center">{item.path}</div>
                        </button>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
