import { useQuery } from '@tanstack/react-query'
import { graphqlClient } from '~/lib/graphql-client'
import { GET_CATEGORY_NAME } from './graphql'

export default function useGetCategoryName(id: string) {
  const { data, isLoading, isError } = useQuery({
    queryKey: ['get-category-name'],
    queryFn: async () => {
      const client = await graphqlClient()
      return client.request({
        document: GET_CATEGORY_NAME,
        variables: {
          id,
        },
      })
    },
    enabled: !!id,
  })

  const categoryName = data?.getCategoryById?.name

  return { categoryName, isLoading, isError }
}
